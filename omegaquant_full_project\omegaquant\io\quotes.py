
from __future__ import annotations
import numpy as np
import pandas as pd

def synthesize_l1_quotes_from_prices(prices: pd.DataFrame, spread_bps: float = 6.0, depth: float = 1_200_000.0, seed: int = 123):
    rng = np.random.RandomState(seed)
    mid = prices
    spr = spread_bps * 1e-4 * mid
    bid = (mid - spr/2.0).clip(lower=0.01)
    ask = (mid + spr/2.0).clip(lower=0.01)
    bid_size = (depth / bid.replace(0, np.nan)).fillna(0.0) * (0.9 + 0.2 * rng.rand(*prices.shape))
    ask_size = (depth / ask.replace(0, np.nan)).fillna(0.0) * (0.9 + 0.2 * rng.rand(*prices.shape))
    return bid, ask, bid_size, ask_size
