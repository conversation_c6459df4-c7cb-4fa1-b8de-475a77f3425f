
from __future__ import annotations
try:
    import jax
    import jax.numpy as jnp
except Exception:
    jax = None; jnp = None

def batch_equity_curves(returns_matrix, weight_sets):
    if jax is None:
        raise ImportError("Install extras 'accel' or 'max' to use JAX acceleration.")
    R = jnp.asarray(returns_matrix); W = jnp.asarray(weight_sets)
    @jax.jit
    def compute(W):
        pr = (R[None, :, :] * W[:, None, :]).sum(axis=2)
        eq = jnp.cumprod(1.0 + pr, axis=1)
        return eq
    return compute(W)
