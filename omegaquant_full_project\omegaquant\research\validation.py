
from __future__ import annotations
import numpy as np
import pandas as pd
from typing import Iterable, <PERSON><PERSON>, List
from itertools import combinations
from scipy.stats import norm

def purged_kfold(n_splits: int, embargo: int, index: Iterable) -> List[Tuple[np.ndarray, np.ndarray]]:
    idx = np.arange(len(index))
    fold_sizes = np.full(n_splits, len(idx) // n_splits, dtype=int)
    fold_sizes[: len(idx) % n_splits] += 1
    blocks = []
    start = 0
    for fs in fold_sizes:
        blocks.append(np.arange(start, start + fs)); start += fs
    splits: List[Tuple[np.ndarray, np.ndarray]] = []
    for b in range(n_splits):
        test = blocks[b]
        mask = np.ones(len(idx), dtype=bool)
        left = max(0, test[0] - embargo); right = min(len(idx) - 1, test[-1] + embargo)
        mask[left : right + 1] = False
        train = idx[mask]; splits.append((train, test))
    return splits

def cpcv(index: Iterable, n_splits: int = 6, k: int = 2, embargo: int = 0) -> List[Tuple[np.ndarray, np.ndarray]]:
    N = len(index); idx = np.arange(N)
    fold_sizes = np.full(n_splits, N // n_splits, dtype=int)
    fold_sizes[: N % n_splits] += 1
    blocks = []; start = 0
    for fs in fold_sizes:
        blocks.append(np.arange(start, start + fs)); start += fs
    splits: List[Tuple[np.ndarray, np.ndarray]] = []
    for test_combo in combinations(range(n_splits), k):
        test = np.concatenate([blocks[i] for i in test_combo])
        mask = np.ones(N, dtype=bool)
        for b in test_combo:
            left = max(0, blocks[b][0] - embargo); right = min(N - 1, blocks[b][-1] + embargo)
            mask[left : right + 1] = False
        train = idx[mask]; splits.append((train, test))
    return splits

def deflated_sharpe(sr: float, n: int, skew: float = 0.0, kurt: float = 3.0) -> float:
    adj = (1 + (skew**2)/6.0 + ((kurt - 3.0)**2)/24.0)
    return sr / np.sqrt(max(1.0, adj * max(1, n - 1)))

def deflated_sharpe_multiple(sr: float, n: int, n_trials: int) -> float:
    if n <= 1 or n_trials <= 1:
        return deflated_sharpe(sr, n)
    z = sr * np.sqrt(n); p = 1 - norm.cdf(z); p_adj = min(1.0, p * n_trials)
    z_adj = norm.isf(p_adj if p_adj > 0 else 1e-300); return float(z_adj / np.sqrt(n))

def probability_of_backtest_overfitting(is_scores: np.ndarray, oos_scores: np.ndarray) -> float:
    assert is_scores.shape == oos_scores.shape
    n_models, n_splits = is_scores.shape; bad = 0
    for s in range(n_splits):
        j_star = np.argmax(is_scores[:, s]); ranks = oos_scores[:, s].argsort().argsort()
        frac = (ranks[j_star] + 1) / (n_models + 1); bad += 1 if frac >= 0.5 else 0
    return float(bad / n_splits)

def leakage_monotonic_index(df: pd.DataFrame) -> bool:
    return not df.index.is_monotonic_increasing

def nested_walkforward(prices: pd.DataFrame, window: int = 504, step: int = 63):
    for start in range(0, len(prices) - window - step + 1, step):
        tr = slice(start, start + window); te = slice(start + window, start + window + step)
        yield (tr, te)
