
from __future__ import annotations
from dataclasses import dataclass
from typing import List, Dict
import numpy as np
import pandas as pd

@dataclass
class Portfolio:
    leverage_max: float = 2.0
    net_max: float = 0.7
    pos_max: float = 0.2
    borrow_rate_annual: float = 1.5  # %

    def initialize(self, index: pd.DatetimeIndex, assets: List[str]):
        self.index = index
        self.assets = assets
        self.positions = pd.DataFrame(0.0, index=index, columns=assets)
        self.cash = pd.Series(0.0, index=index)
        self._last_row = None

    def _carry_forward(self, when: pd.Timestamp):
        i = self.index.get_loc(when)
        if i == 0:  # first row
            return
        prev = self.index[i-1]
        if self._last_row != when:
            self.positions.loc[when] = self.positions.loc[prev].values
            self.cash.loc[when] = self.cash.loc[prev]
            self._last_row = when

    def apply_fills(self, when: pd.Timestamp, fills: Dict[str, tuple[float, float]]):
        self._carry_forward(when)
        for a, (q, px) in fills.items():
            self.positions.loc[when, a] += q
            self.cash.loc[when] -= q * px  # all-in price already includes fees/impact

    def accrue_borrow(self, when: pd.Timestamp, price_row: pd.Series):
        """Accrue daily borrow on shorts: r/365 * |short notional| deducted from cash."""
        self._carry_forward(when)
        short_notional = np.sum(np.clip(-self.positions.loc[when].values, 0, None) * price_row.values)
        daily = (self.borrow_rate_annual / 100.0) / 365.0 * short_notional
        self.cash.loc[when] -= daily

    def enforce_constraints(self, when: pd.Timestamp, price_row: pd.Series):
        """Post-trade constraint enforcement: clip per-position and scale to leverage_max/net_max if needed."""
        self._carry_forward(when)
        pos_vals = self.positions.loc[when] * price_row
        equity = float(self.cash.loc[when] + pos_vals.sum())
        if equity <= 0:
            return
        # Clip per-position by fraction of equity
        max_pos_value = self.pos_max * equity
        for a in self.assets:
            v = float(self.positions.loc[when, a] * price_row[a])
            if abs(v) > max_pos_value and price_row[a] != 0:
                self.positions.loc[when, a] = np.sign(v) * max_pos_value / price_row[a]
        # Gross/net checks
        pos_vals = self.positions.loc[when] * price_row
        gross = np.sum(np.abs(pos_vals)) / equity
        net = float(pos_vals.sum() / equity)
        if gross > self.leverage_max + 1e-9:
            scale = self.leverage_max / gross
            self.positions.loc[when] *= scale
        if abs(net) > self.net_max + 1e-9:
            adj = self.net_max / (abs(net) + 1e-12)
            self.positions.loc[when] *= adj

    def mark_to_market(self, prices: pd.DataFrame) -> pd.Series:
        """Compute equity curve from positions and cash."""
        eq = pd.Series(0.0, index=self.index)
        cash_ffill = self.cash.ffill().fillna(0.0)
        for t in self.index:
            eq.loc[t] = cash_ffill.loc[t] + float((self.positions.loc[t] * prices.loc[t]).sum())
        return eq
