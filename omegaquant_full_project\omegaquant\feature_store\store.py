
from __future__ import annotations
from pathlib import Path
from typing import Dict, Any, List
import json
import pandas as pd

MANIFEST = Path("features") / "manifest.json"

def _load_manifest() -> Dict[str, Any]:
    if MANIFEST.exists():
        return json.loads(MANIFEST.read_text())
    return {"features": {}}

def _save_manifest(m: Dict[str, Any]) -> None:
    MANIFEST.parent.mkdir(parents=True, exist_ok=True)
    MANIFEST.write_text(json.dumps(m, indent=2))

def register(name: str, path: str, metadata: Dict[str, Any]) -> None:
    m = _load_manifest()
    m["features"][name] = {"path": path, "meta": metadata}
    _save_manifest(m)

def list_features() -> List[str]:
    return list(_load_manifest()["features"].keys())

def load(name: str) -> pd.DataFrame:
    m = _load_manifest()
    info = m["features"].get(name)
    if not info:
        raise FileNotFoundError(f"Feature '{name}' not registered.")
    p = Path(info["path"])
    if p.suffix == ".parquet":
        return pd.read_parquet(p)
    else:
        return pd.read_csv(p, parse_dates=True, index_col=0)

def save(name: str, df: pd.DataFrame, path: str, metadata: Dict[str, Any]) -> None:
    p = Path(path)
    p.parent.mkdir(parents=True, exist_ok=True)
    if p.suffix == ".parquet":
        df.to_parquet(p)
    else:
        df.to_csv(p)
    register(name, path, metadata)
