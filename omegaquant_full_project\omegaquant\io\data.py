
from __future__ import annotations
import numpy as np
import pandas as pd

def _index(n_days: int, start: str = "2015-01-01") -> pd.DatetimeIndex:
    return pd.date_range(start=start, periods=n_days, freq="B", tz="UTC")

def synthetic_gbm(tickers, n_days: int = 252*5, seed: int = 42, mu: float = 0.08, sigma: float = 0.2) -> pd.DataFrame:
    rng = np.random.RandomState(seed)
    dt = 1/252
    idx = _index(n_days)
    P = pd.DataFrame(index=idx, columns=tickers, dtype=float)
    for a in tickers:
        shocks = rng.normal((mu - 0.5*sigma**2)*dt, sigma*np.sqrt(dt), size=n_days)
        px = 100 * np.exp(np.cumsum(shocks))
        P[a] = px
    return P

def synthetic_heston(tickers, n_days: int = 252*5, seed: int = 123, v0: float = 0.04, kappa: float = 1.5, theta: float = 0.04, xi: float = 0.5, rho: float = -0.5):
    rng = np.random.RandomState(seed)
    dt = 1/252
    idx = _index(n_days)
    P = pd.DataFrame(index=idx, columns=tickers, dtype=float)
    for a in tickers:
        v = v0; s = 100.0
        series = []
        for _ in range(n_days):
            z1 = rng.normal(); z2 = rho*z1 + np.sqrt(1-rho**2)*rng.normal()
            v = max(1e-8, v + kappa*(theta - v)*dt + xi*np.sqrt(v*dt)*z2)
            s = s * np.exp((-0.5*v*dt) + np.sqrt(v*dt)*z1)
            series.append(s)
        P[a] = series
    return P

def microstructure_noise(prices: pd.DataFrame, spread_bps: float = 5.0, seed: int = 0) -> pd.DataFrame:
    """Inject simple microstructure noise: random +/- half-spread around mid."""
    rng = np.random.RandomState(seed)
    spr = spread_bps * 1e-4 * prices
    noise = (rng.randint(0, 2, size=prices.shape)*2 - 1) * spr/2.0
    return (prices + noise).clip(lower=0.01)
