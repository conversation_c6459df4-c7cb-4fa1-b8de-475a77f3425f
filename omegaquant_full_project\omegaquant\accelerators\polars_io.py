
from __future__ import annotations
from typing import Optional
import pandas as pd

def read_prices(path: str, date_col: str = "date", columns: Optional[list[str]] = None) -> pd.DataFrame:
    try:
        import polars as pl
        df = pl.read_parquet(path) if path.endswith(".parquet") else pl.read_csv(path, try_parse_dates=True)
        if date_col in df.columns:
            df = df.with_columns(pl.col(date_col).str.strptime(pl.Datetime, strict=False, exact=False).dt.replace_time_zone("UTC"))
            df = df.rename({date_col: "date"}).sort("date")
            if columns:
                keep = ["date"] + [c for c in columns if c in df.columns]
                df = df.select(keep)
            pdf = df.to_pandas(); pdf = pdf.set_index("date")
            return pdf
        else:
            return df.to_pandas()
    except Exception:
        pdf = pd.read_csv(path)
        if date_col in pdf.columns:
            pdf["date"] = pd.to_datetime(pdf[date_col], utc=True); pdf = pdf.set_index("date")
        return pdf.sort_index()
