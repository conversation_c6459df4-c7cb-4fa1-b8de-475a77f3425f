
from __future__ import annotations
from dataclasses import dataclass
from pathlib import Path
import time
from typing import Dict, Any
import numpy as np
import pandas as pd
import yaml

from ..core.portfolio import Portfolio
from ..core.execution import ExecutionEngine, generate_orders_from_target
from ..core.costs import CostModel
from ..research import signals as sig
from ..reporting.metrics import tear_sheet, basic_metrics
from ..io.data import synthetic_gbm, synthetic_heston, microstructure_noise

@dataclass
class BacktestResult:
    equity: pd.Series
    returns: pd.Series
    trades: pd.DataFrame
    metrics: Dict[str, float]
    out_dir: Path

def _ensure_outdir() -> Path:
    ts = time.strftime("%Y%m%d-%H%M%S")
    out = Path("outputs") / f"run-{ts}"
    out.mkdir(parents=True, exist_ok=True)
    return out

def backtest_from_config(path: str | Path) -> BacktestResult:
    with open(path, "r") as f:
        cfg = yaml.safe_load(f)
    return run_backtest(cfg)

def run_backtest(cfg: Dict[str, Any]) -> BacktestResult:
    seed = int(cfg.get("seed", 42))
    rng = np.random.RandomState(seed)
    tickers = cfg.get("tickers", ["AAPL","MSFT","AMZN","GOOG","META","NFLX","NVDA","TSLA","ORCL","IBM"])
    n_days = int(cfg.get("n_days", 252*5))
    data_mode = cfg.get("data", "synthetic_equities")
    cost = CostModel(commission_bps=float(cfg.get("commission_bps", 1.0)))

    if data_mode == "synthetic_equities":
        prices = synthetic_gbm(tickers, n_days, seed=seed)
    elif data_mode == "heston":
        prices = synthetic_heston(tickers, n_days, seed=seed)
    else:
        prices = synthetic_gbm(tickers, n_days, seed=seed)
    if cfg.get("microstructure", False):
        prices = microstructure_noise(prices, spread_bps=cfg.get("spread_bps", 5.0), seed=seed+1)
    volumes = pd.DataFrame(1_000_000.0, index=prices.index, columns=prices.columns)

    s1 = sig.tsmom(prices, lookback=126)
    s2 = sig.xsec_momentum(prices, lookback=126)
    s3 = sig.mean_reversion_z(prices, lookback=20)
    signal = sig.combine_signals({"tsmom":0.4,"xsec":0.4,"meanrev":0.2}, tsmom=s1, xsec=s2, meanrev=s3)
    targets = sig.target_shares_from_signal(signal, prices, target_vol=cfg.get("target_vol", 0.10), max_gross=cfg.get("max_gross", 2.0))

    port = Portfolio(leverage_max=cfg.get("max_gross", 2.0), net_max=cfg.get("max_net", 0.7), pos_max=cfg.get("pos_max", 0.2))
    port.initialize(prices.index, list(prices.columns))
    engine = ExecutionEngine(cost_model=cost, rng=rng)

    all_trades = []
    for when in prices.index:
        port.accrue_borrow(when, prices.loc[when])
        orders = generate_orders_from_target(port.positions.loc[when], targets.loc[when])
        # log desired per asset even for baseline
        for a, o in orders.items():
            all_trades.append({"time":when, "asset":a, "desired_qty": float(o.qty), "qty": 0.0, "px": float("nan")})
        fills = engine.simulate_bar(when, prices.loc[when], volumes.loc[when], orders)
        if fills:
            port.apply_fills(when, fills)
            for a,(q,px) in fills.items():
                all_trades.append({"time":when, "asset":a, "desired_qty": float(orders[a].qty), "qty": float(q), "px": float(px)})
        port.enforce_constraints(when, prices.loc[when])

    equity = port.mark_to_market(prices)
    rets = equity.pct_change().fillna(0.0)
    trades = pd.DataFrame(all_trades).set_index("time") if all_trades else pd.DataFrame(columns=["time","asset","desired_qty","qty","px"]).set_index("time")
    if "desired_qty" in trades.columns:
        trades["fill_rate"] = (trades["qty"].abs() / trades["desired_qty"].abs().replace(0.0, np.nan) * 100).fillna(0.0)

    out_dir = _ensure_outdir()
    equity.to_csv(out_dir / "equity.csv"); rets.to_csv(out_dir / "returns.csv")
    trades.to_csv(out_dir / "trades.csv"); prices.to_csv(out_dir / "prices.csv")
    tear_sheet(equity, rets, trades, out_path=out_dir / "report.html")
    mets = basic_metrics(rets)
    with open(out_dir / "metrics.yaml","w") as f:
        import yaml as _y; _y.safe_dump({k: float(v) for k,v in mets.items()}, f)
    return BacktestResult(equity=equity, returns=rets, trades=trades, metrics=mets, out_dir=out_dir)
