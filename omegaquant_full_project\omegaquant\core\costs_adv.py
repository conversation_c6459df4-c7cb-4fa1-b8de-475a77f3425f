
from __future__ import annotations
from dataclasses import dataclass

@dataclass
class AdvCostModel:
    commission_bps: float = 1.0
    maker_fee_bps: float = -0.2
    taker_fee_bps: float = 2.5
    exchange_fee_bps: float = 0.0
    borrow_rate_annual: float = 1.5  # percent

    def all_in_price(self, mid: float, side: float, cross_spread: bool, impact_bps: float) -> float:
        fee_bps = (self.taker_fee_bps if cross_spread else self.maker_fee_bps) + self.commission_bps + self.exchange_fee_bps
        px = mid * (1.0 + side * (impact_bps * 1e-4))
        px = px * (1.0 + side * (fee_bps * 1e-4))
        return float(px)
