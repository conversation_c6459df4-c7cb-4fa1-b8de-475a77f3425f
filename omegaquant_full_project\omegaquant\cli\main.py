
from __future__ import annotations
import argparse
from pathlib import Path
import yaml
import pandas as pd

from ..engine.backtest import backtest_from_config
from ..engine.max_backtest import run_backtest_max
from ..reporting.tca import tca_from_files
from ..reporting.attribution import write_report
from ..feature_store import store as fstore

def cmd_init(args):
    cfg = """# OmegaQuant example config
seed: 42
data: synthetic_equities
tickers: [AAPL, MSFT, AMZN, GOOG, META, NFLX, NVDA, TSLA, ORCL, IBM]
n_days: 1260
commission_bps: 1.0
target_vol: 0.10
max_gross: 2.0
max_net: 0.7
pos_max: 0.2
microstructure: true
spread_bps: 5.0
"""
    p = Path(args.path or "examples/configs/equities.yaml")
    p.parent.mkdir(parents=True, exist_ok=True)
    p.write_text(cfg)
    print(f"Wrote example config to {p}")

def cmd_run(args):
    res = backtest_from_config(args.config)
    print("Run complete.")
    print(f"Outputs: {res.out_dir}")
    for k,v in res.metrics.items():
        print(f"{k}: {v:.4f}")

def cmd_run_max(args):
    with open(args.config, "r") as f:
        cfg = yaml.safe_load(f)
    res = run_backtest_max(cfg)
    print("MAX run complete.")
    print(f"Outputs: {res.out_dir}")
    for k,v in res.metrics.items():
        print(f"{k}: {v:.4f}")

def cmd_report(args):
    out_dir = Path(args.dir)
    trades_csv = out_dir / "trades.csv"
    prices_csv = out_dir / "prices.csv"
    out_html = out_dir / "tca.html"
    tca_from_files(trades_csv, prices_csv, out_html)
    print(f"TCA written to {out_html}")

def cmd_report_attrib(args):
    out_dir = Path(args.dir)
    prices = pd.read_csv(out_dir / "prices.csv", parse_dates=True, index_col=0)
    returns = prices.pct_change().fillna(0.0)
    bench = pd.Series(1.0/returns.shape[1], index=returns.columns)
    from ..reporting.attribution import brinson_allocation_selection
    pw = returns*0 + 1/returns.shape[1]  # placeholder weights for demo
    attrib = brinson_allocation_selection(returns, bench, pw)
    write_report(attrib, None, out_dir / "attribution.html")
    print(f"Attribution written to {out_dir / 'attribution.html'}")

def cmd_features(args):
    if args.sub == "list":
        print("\n".join(fstore.list_features()))
    elif args.sub == "register":
        fstore.register(args.name, args.path, {"desc": args.desc})
        print(f"Registered {args.name} -> {args.path}")
    elif args.sub == "save":
        df = pd.read_csv(args.src, parse_dates=True, index_col=0)
        fstore.save(args.name, df, args.path, {"desc": args.desc})
        print(f"Saved & registered {args.name} -> {args.path}")
    elif args.sub == "load":
        df = fstore.load(args.name)
        print(df.head())

def main():
    ap = argparse.ArgumentParser("omegaquant")
    sub = ap.add_subparsers(dest="cmd")

    ap_init = sub.add_parser("init", help="write a starter config")
    ap_init.add_argument("--path", type=str, default=None)
    ap_init.set_defaults(func=cmd_init)

    ap_run = sub.add_parser("run", help="run a backtest from config")
    ap_run.add_argument("--config", type=str, required=True)
    ap_run.set_defaults(func=cmd_run)

    ap_runmax = sub.add_parser("run-max", help="run the MAX backtest (L1 + regimes + risk models)")
    ap_runmax.add_argument("--config", type=str, required=True)
    ap_runmax.set_defaults(func=cmd_run_max)

    ap_report = sub.add_parser("report", help="build TCA for a run dir")
    ap_report.add_argument("--dir", type=str, required=True)
    ap_report.set_defaults(func=cmd_report)

    ap_rat = sub.add_parser("report-attrib", help="build attribution report (experimental)")
    ap_rat.add_argument("--dir", type=str, required=True)
    ap_rat.set_defaults(func=cmd_report_attrib)

    ap_feat = sub.add_parser("features", help="feature store operations")
    feat_sub = ap_feat.add_subparsers(dest="sub")
    feat_list = feat_sub.add_parser("list"); feat_list.set_defaults(func=cmd_features)
    feat_reg = feat_sub.add_parser("register")
    feat_reg.add_argument("--name", required=True); feat_reg.add_argument("--path", required=True); feat_reg.add_argument("--desc", default="")
    feat_reg.set_defaults(func=cmd_features)
    feat_save = feat_sub.add_parser("save")
    feat_save.add_argument("--name", required=True); feat_save.add_argument("--src", required=True)
    feat_save.add_argument("--path", required=True); feat_save.add_argument("--desc", default="")
    feat_save.set_defaults(func=cmd_features)
    feat_load = feat_sub.add_parser("load")
    feat_load.add_argument("--name", required=True)
    feat_load.set_defaults(func=cmd_features)

    args = ap.parse_args()
    if hasattr(args, "func"):
        args.func(args)
    else:
        ap.print_help()

if __name__ == "__main__":
    main()
