
from __future__ import annotations
import numpy as np
import pandas as pd
from typing import Tuple
from scipy.cluster.hierarchy import linkage, leaves_list
from scipy.spatial.distance import squareform
from scipy.optimize import linprog

def ledoit_wolf_cov(returns: pd.DataFrame) -> pd.DataFrame:
    X = returns.fillna(0.0).values
    T, N = X.shape
    sample = np.cov(X, rowvar=False, ddof=1)
    mu = np.trace(sample) / N
    delta = np.sum((sample - mu * np.eye(N))**2)
    beta = np.sum((X**2).dot((X**2).T)) / (T**2) - (np.sum(sample**2))
    shrink = max(0.0, min(1.0, beta / max(delta, 1e-12)))
    shrunk = shrink * mu * np.eye(N) + (1 - shrink) * sample
    return pd.DataFrame(shrunk, index=returns.columns, columns=returns.columns)

def cov_to_corr(cov: pd.DataFrame) -> pd.DataFrame:
    d = np.sqrt(np.diag(cov.values)) + 1e-12
    return cov / d / d[:, None]

def _quasi_diag(link: np.ndarray) -> np.ndarray:
    return leaves_list(link)

def hrp_weights(cov: pd.DataFrame) -> pd.Series:
    corr = cov_to_corr(cov); dist = np.sqrt(0.5 * (1 - corr.clip(-1, 1)))
    link = linkage(squareform(dist.values, checks=False), "ward")
    order = _quasi_diag(link); ordered = cov.values[order][:, order]
    def _rec(c):
        n=c.shape[0]
        if n==1: return np.array([1.0])
        m=n//2; l=_rec(c[:m,:m]); r=_rec(c[m:,m:])
        vl=l@c[:m,:m]@l; vr=r@c[m:,m:]@r; a=1.0-vl/(vl+vr+1e-12)
        return np.concatenate([l*a, r*(1-a)])
    w = pd.Series(_rec(ordered), index=cov.index[order]); return w/w.sum()

def cvar_minimization(returns: pd.DataFrame, alpha: float = 0.05, long_only: bool = True,
                      gross_limit: float = 1.0, bounds: Tuple[float, float] = (0.0, 0.2)) -> pd.Series:
    R = returns.fillna(0.0).values; T, N = R.shape; n_vars = N + 1 + T
    c = np.zeros(n_vars); c[N] = 1.0; c[N+1:] = 1.0 / ((1 - alpha) * T)
    A = []; b = []
    for t in range(T):
        row = np.zeros(n_vars); row[:N] = -R[t]; row[N] = -1.0; row[N+1+t] = 1.0
        A.append(row); b.append(0.0)
    for t in range(T):
        row = np.zeros(n_vars); row[N+1+t] = -1.0; A.append(row); b.append(0.0)
    A_eq = None; b_eq = None
    if long_only:
        A_eq = np.zeros((1, n_vars)); A_eq[0,:N] = 1.0; b_eq = np.array([1.0])
    lb, ub = bounds
    bounds_list = [(lb, ub) if long_only else (-ub, ub)] * N + [(-1e3, 1e3)] + [(0.0, 1e3)] * T
    res = linprog(c, A_ub=np.array(A), b_ub=np.array(b), A_eq=A_eq, b_eq=b_eq,
                  bounds=bounds_list, method="highs", options={"presolve": True})
    if not res.success:
        w = np.ones(N) / N
    else:
        w = res.x[:N]
        if not long_only:
            s = np.sum(np.abs(w))
            if s > gross_limit + 1e-12: w = w * (gross_limit / s)
    return pd.Series(w, index=returns.columns)
