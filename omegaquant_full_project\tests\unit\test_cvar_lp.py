
import numpy as np, pandas as pd
from omegaquant.research.risk_models import cvar_minimization
def test_cvar_lp_shapes():
    rng = np.random.RandomState(1)
    R = pd.DataFrame(rng.normal(scale=0.01, size=(300,4)), columns=list("WXYZ"))
    w = cvar_minimization(R, alpha=0.1, long_only=True, bounds=(0.0,0.8))
    assert set(w.index) == set(R.columns)
    assert (w >= 0).all()
    assert abs(w.sum() - 1.0) < 1e-6
