
from __future__ import annotations
from dataclasses import dataclass
from typing import Di<PERSON>, <PERSON><PERSON>, Optional, List
import numpy as np
import pandas as pd

from .costs_adv import AdvCostModel
from .costs import square_root_impact_bps, almgren_chriss_schedule
from .execution import Order, OrderType

try:
    import numba as nb  # type: ignore
except Exception:
    nb = None  # type: ignore

if nb is not None:
    @nb.njit
    def _fill_capacity_nb(qty: float, is_buy: int, bid_size: float, ask_size: float,
                          latency_ms: float, queue_ahead: float, venue_liq: float) -> float:
        depth = ask_size if is_buy == 1 else bid_size
        accessible = max(0.0, venue_liq) * depth
        after_queue = max(0.0, (1.0 - min(1.0, queue_ahead))) * accessible
        latency_frac = max(0.0, 1.0 - min(1.0, latency_ms / 1000.0))
        cap = after_queue * latency_frac
        return min(abs(qty), cap)

    @nb.njit
    def _almgren_chriss_schedule_nb(total_qty: float, n_slices: int, risk_aversion: float) -> np.ndarray:
        if n_slices <= 1:
            return np.array([total_qty], dtype=np.float64)
        sgn = 1.0 if total_qty >= 0 else -1.0
        q = abs(total_qty)
        out = np.empty(n_slices, dtype=np.float64)
        if risk_aversion <= 1e-12:
            each = q / n_slices
            for i in range(n_slices):
                out[i] = sgn * each
            return out
        lam = max(1e-6, risk_aversion)
        weights = np.empty(n_slices, dtype=np.float64)
        s = 0.0
        for i in range(n_slices):
            w = np.exp(-lam * i); weights[i] = w; s += w
        for i in range(n_slices):
            weights[i] /= s; out[i] = sgn * q * weights[i]
        return out

    @nb.njit
    def _sqrt_impact_bps_nb(q_dollar: float, adv_dollar: float, k: float = 10.0) -> float:
        if adv_dollar <= 0.0 or q_dollar <= 0.0:
            return 0.0
        qd = q_dollar if q_dollar < adv_dollar else adv_dollar
        return k * np.sqrt(qd / adv_dollar)

    @nb.njit
    def _simulate_bar_l1_numba(
        bid: np.ndarray, ask: np.ndarray, bid_size: np.ndarray, ask_size: np.ndarray,
        adv_dollar: np.ndarray, order_qtys: np.ndarray, latencies: np.ndarray, queues: np.ndarray,
        venue_liqs: np.ndarray, ac_slices: int, ac_risk: float, commission_bps: float,
        maker_fee_bps: float, taker_fee_bps: float, exchange_fee_bps: float
    ) -> Tuple[np.ndarray, np.ndarray]:
        n = bid.shape[0]
        filled_qtys = np.zeros(n, dtype=np.float64)
        exec_pxs = np.zeros(n, dtype=np.float64)
        for i in range(n):
            q = order_qtys[i]
            if not (q != 0.0):
                continue
            b = bid[i]; a = ask[i]
            if not (b > 0.0 and a > 0.0):
                continue
            mid = 0.5 * (a + b)
            bs = bid_size[i]; asz = ask_size[i]
            adv = adv_dollar[i] if adv_dollar.size == n else mid * (bs + asz)
            sched = _almgren_chriss_schedule_nb(q, ac_slices, ac_risk)
            tot_fill = 0.0; vwap_px_times_qty = 0.0
            for s in range(sched.shape[0]):
                sl = sched[s]
                side = 1 if sl > 0 else -1
                cap = _fill_capacity_nb(sl, side, bs, asz, latencies[i], queues[i], venue_liqs[i])
                if cap <= 0.0: continue
                signed = side * cap
                px_ref = a if side == 1 else b
                fee_bps = (taker_fee_bps) + commission_bps + exchange_fee_bps
                impact_bps = _sqrt_impact_bps_nb(abs(signed) * mid, adv)
                exec_px = px_ref * (1.0 + side * (impact_bps * 1e-4))
                exec_px = exec_px * (1.0 + side * (fee_bps * 1e-4))
                tot_fill += signed; vwap_px_times_qty += exec_px * abs(signed)
            if abs(tot_fill) > 0.0:
                filled_qtys[i] = tot_fill
                exec_pxs[i] = vwap_px_times_qty / abs(tot_fill)
        return filled_qtys, exec_pxs
else:
    def _simulate_bar_l1_numba(*args, **kwargs):
        raise ImportError("Numba not available")

def simulate_bar_l1(
    when: pd.Timestamp,
    bid: pd.Series, ask: pd.Series,
    bid_size: pd.Series, ask_size: pd.Series,
    adv_dollar: pd.Series,
    orders: Dict[str, Order],
    cost_model: AdvCostModel,
    ac_slices: int = 1,
    ac_risk_aversion: float = 0.0,
    rng: Optional[np.random.RandomState] = None,
    use_numba: bool = False,
) -> Dict[str, Tuple[float, float]]:
    if use_numba and nb is not None and len(orders) > 0:
        assets: List[str] = list(orders.keys())
        to_arr = lambda s: np.array([float(s.get(a, np.nan)) for a in assets], dtype=np.float64)
        bid_arr, ask_arr = to_arr(bid), to_arr(ask)
        bs_arr, as_arr = to_arr(bid_size), to_arr(ask_size)
        adv_arr = to_arr(adv_dollar)
        qtys = np.array([orders[a].qty for a in assets], dtype=np.float64)
        lats = np.array([orders[a].latency_ms for a in assets], dtype=np.float64)
        ques = np.array([orders[a].queue_ahead for a in assets], dtype=np.float64)
        vliqs = np.array([orders[a].venue_liquidity for a in assets], dtype=np.float64)
        fq, px = _simulate_bar_l1_numba(
            bid_arr, ask_arr, bs_arr, as_arr, adv_arr, qtys, lats, ques, vliqs,
            int(ac_slices), float(ac_risk_aversion),
            float(cost_model.commission_bps), float(cost_model.maker_fee_bps),
            float(cost_model.taker_fee_bps), float(cost_model.exchange_fee_bps)
        )
        fills: Dict[str, Tuple[float, float]] = {}
        for i, a in enumerate(assets):
            if fq[i] != 0.0:
                fills[a] = (float(fq[i]), float(px[i]))
        return fills

    fills: Dict[str, Tuple[float, float]] = {}
    for a, o in orders.items():
        b, a_px = float(bid.get(a, np.nan)), float(ask.get(a, np.nan))
        bs, asz = float(bid_size.get(a, 0.0)), float(ask_size.get(a, 0.0))
        if np.isnan(b) or np.isnan(a_px) or a_px <= 0 or b <= 0 or o.qty == 0.0:
            continue
        mid = 0.5 * (a_px + b)
        v_dollar = float(adv_dollar.get(a, mid * (bs + asz)))
        sched = almgren_chriss_schedule(o.qty, max(1, ac_slices), risk_aversion=ac_risk_aversion)
        total_filled = 0.0; vwap_px = 0.0
        for sl in sched:
            side = 1 if sl > 0 else -1
            depth = asz if side == 1 else bs
            accessible = max(0.0, o.venue_liquidity) * depth
            after_queue = max(0.0, (1.0 - min(1.0, o.queue_ahead))) * accessible
            latency_frac = max(0.0, 1.0 - min(1.0, o.latency_ms / 1000.0))
            cap = after_queue * latency_frac
            cap = min(abs(sl), cap)
            if cap <= 0.0: continue
            signed = side * cap
            px_ref = a_px if side == 1 else b
            fee_bps = (cost_model.taker_fee_bps) + cost_model.commission_bps + cost_model.exchange_fee_bps
            impact_bps = square_root_impact_bps(abs(signed) * mid, dollar_vol=max(v_dollar, 1e-9))
            exec_px = px_ref * (1.0 + side * (impact_bps * 1e-4))
            exec_px = exec_px * (1.0 + side * (fee_bps * 1e-4))
            total_filled += signed; vwap_px += exec_px * abs(signed)
        if abs(total_filled) > 0.0:
            fills[a] = (total_filled, vwap_px / abs(total_filled))
    return fills
