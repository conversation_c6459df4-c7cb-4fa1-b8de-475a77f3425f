
# OmegaQuant

Research‑grade algorithmic trading stack with **advanced microstructure**, **rigorous validation**, and **accelerated research loops**.

- Baseline: pandas/NumPy + Numba.
- MAX path: L1 quotes (bid/ask/size), maker/taker fees, queue/latency, **<PERSON><PERSON><PERSON>–Chriss** slicing, square‑root impact; HRP/LED‑Wolf/CVaR; JAX‑batched sweeps; Polars/Arrow I/O.

## Quick start
```bash
python -m venv .venv && source .venv/bin/activate
pip install -e ".[max,dev]"

# Baseline
omegaquant run --config examples/configs/equities.yaml

# MAX
omegaquant run-max --config examples/configs/equities_max.yaml

# Reports
omegaquant report --dir outputs/run-YYYYMMDD-HHMMSS[-max]
omegaquant report-attrib --dir outputs/run-YYYYMMDD-HHMMSS[-max]
```
