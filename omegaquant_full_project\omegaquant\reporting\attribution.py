
from __future__ import annotations
import numpy as np
import pandas as pd
from jinja2 import Template
from pathlib import Path

_HTML = Template("""
<!doctype html><html><head><meta charset="utf-8"><title>OmegaQuant Attribution</title>
<style>body{font-family:system-ui,Arial} table{border-collapse:collapse} th,td{padding:6px 10px;border:1px solid #ddd}</style>
</head><body>
<h1>Performance Attribution</h1>
<p>Brinson‑like allocation/selection decomposition.</p>
<h2>Allocation vs Selection</h2>
{{ table.to_html(float_format=lambda x: f"{x:.6f}") }}
<p>Educational use only.</p>
</body></html>
""")

def brinson_allocation_selection(asset_returns: pd.DataFrame, benchmark_weights: pd.Series, portfolio_weights: pd.DataFrame) -> pd.DataFrame:
    r_b = (asset_returns * benchmark_weights.reindex(asset_returns.columns).fillna(0.0)).sum(axis=1)
    alloc = (portfolio_weights.subtract(benchmark_weights, axis=1)) * r_b.values.reshape(-1,1)
    sel = (benchmark_weights.reindex(asset_returns.columns).fillna(0.0)) * (asset_returns.subtract(r_b, axis=0))
    out = pd.DataFrame({"allocation": alloc.sum(axis=1), "selection": sel.sum(axis=1)}, index=asset_returns.index)
    return out

def write_report(attrib: pd.DataFrame, tca_summary: pd.DataFrame | None, out_html: Path):
    html = _HTML.render(table=attrib)
    Path(out_html).write_text(html, encoding="utf-8")
