
from __future__ import annotations
import numpy as np
import pandas as pd
from pathlib import Path
from jinja2 import Template
from ..core.costs_adv import AdvCostModel

_HTML = Template("""
<!doctype html><html><head><meta charset="utf-8"><title>OmegaQuant TCA</title>
<style>body{font-family:system-ui,Arial} table{border-collapse:collapse} th,td{padding:6px 10px;border:1px solid #ddd}</style>
</head><body>
<h1>Transaction Cost Analysis</h1>
<p>Slippage vs same‑bar mid. Positive bps is unfavorable for buys and favorable for sells (signed).</p>
<h2>Summary (by asset)</h2>
{{ summary.to_html(float_format=lambda x: f"{x:.4f}") }}
<h2>Component Means (bps)</h2>
{{ comps.to_html(float_format=lambda x: f"{x:.4f}") }}
<h2>Sample Trades</h2>
{{ trades.head(40).to_html(float_format=lambda x: f"{x:.6f}") }}
<p>Educational use only.</p>
</body></html>
""")

def _ensure_dt(s):
    s = pd.to_datetime(s, utc=True)
    if s.tz is None:
        s = s.tz_localize("UTC")
    return s

def _lookup_mid(prices: pd.DataFrame, idx: pd.DatetimeIndex, assets: pd.Series) -> np.ndarray:
    P = prices.reindex(idx, method="ffill")
    mids = np.empty(len(idx))
    for i,(t,a) in enumerate(zip(idx, assets)):
        mids[i] = float(P.loc[t].get(a, np.nan)) if t in P.index else np.nan
    return mids

def _component_breakdown(trades: pd.DataFrame, prices: pd.DataFrame, run_dir: Path) -> pd.DataFrame:
    comps = pd.DataFrame(index=trades.index, columns=["spread_bps","fees_bps","impact_bps","borrow_bps"], dtype=float)
    # Try to load L1 artifacts for better estimates
    bidf = run_dir / "bid.csv"; askf = run_dir / "ask.csv"; bfs = run_dir / "bid_size.csv"; afs = run_dir / "ask_size.csv"
    bid = ask = bid_size = ask_size = None
    if bidf.exists() and askf.exists():
        bid = pd.read_csv(bidf, parse_dates=True, index_col=0)
        ask = pd.read_csv(askf, parse_dates=True, index_col=0)
    if bfs.exists() and afs.exists():
        bid_size = pd.read_csv(bfs, parse_dates=True, index_col=0)
        ask_size = pd.read_csv(afs, parse_dates=True, index_col=0)

    cost = AdvCostModel()
    # fees (maker/taker unknown ex-post; assume taker)
    comps["fees_bps"] = cost.taker_fee_bps + cost.commission_bps + cost.exchange_fee_bps

    # spread: use half-spread from L1 if available; else proxy from |px - mid| * 2
    if bid is not None and ask is not None:
        mids = _lookup_mid((bid+ask)/2.0, trades.index, trades["asset"])
        half = (ask.reindex(trades.index, method="ffill").lookup(trades.index, trades["asset"]) -
                bid.reindex(trades.index, method="ffill").lookup(trades.index, trades["asset"])) / 2.0
        comps["spread_bps"] = 1e4 * (np.abs(half) / mids)
    else:
        comps["spread_bps"] = np.abs(trades["px"] - trades["mid"]) / trades["mid"] * 1e4

    # impact: sqrt model using ADV ≈ mid*(bid_size+ask_size)
    if bid_size is not None and ask_size is not None and bid is not None and ask is not None:
        mids = _lookup_mid((bid+ask)/2.0, trades.index, trades["asset"])
        depth = (bid_size.reindex(trades.index, method="ffill").values +
                 ask_size.reindex(trades.index, method="ffill").values)
        adv = mids * depth.mean(axis=1) if depth.ndim==2 else mids * depth
        qd = np.abs(trades["qty"].values) * mids
        with np.errstate(invalid="ignore"):
            comps["impact_bps"] = 10.0 * np.sqrt(np.clip(qd, 0, adv) / np.where(adv<=0, np.nan, adv))
        comps["impact_bps"] = comps["impact_bps"].fillna(0.0)
    else:
        comps["impact_bps"] = 0.0

    # borrow: simple per-trade proxy (position-level accrual handled elsewhere)
    comps["borrow_bps"] = 1e4 * (cost.borrow_rate_annual/100.0) / 365.0 * (trades["qty"] < 0).astype(float)
    return comps

def tca_from_files(trades_csv: Path | str, prices_csv: Path | str, out_html: Path | str) -> None:
    trades = pd.read_csv(trades_csv, parse_dates=["time"], index_col="time")
    prices = pd.read_csv(prices_csv, parse_dates=True, index_col=0)
    trades = trades.sort_index()
    # mid at trade times
    mids = _lookup_mid(prices, trades.index, trades["asset"])
    trades["mid"] = mids
    trades["side"] = np.sign(trades["qty"]).astype(float)
    trades["notional"] = np.abs(trades["qty"] * trades["mid"])
    trades["slippage_bps"] = trades["side"] * (trades["px"] - trades["mid"]) / trades["mid"] * 1e4

    # Component breakdown (if MAX artifacts are present)
    run_dir = Path(trades_csv).parent
    comps = _component_breakdown(trades, prices, run_dir)
    comps_mean = comps.mean().to_frame("mean_bps").T

    summary = trades.groupby("asset").agg(
        trades=("qty","count"),
        notional_usd=("notional","sum"),
        slippage_bps_mean=("slippage_bps","mean"),
        slippage_bps_p95=("slippage_bps", lambda x: np.percentile(x, 95)),
        slippage_bps_p05=("slippage_bps", lambda x: np.percentile(x, 5)),
    ).sort_values("notional_usd", ascending=False)

    html = _HTML.render(summary=summary, trades=trades, comps=comps_mean)
    Path(out_html).write_text(html, encoding="utf-8")
