
from __future__ import annotations
import numpy as np
import pandas as pd
from pathlib import Path
from jinja2 import Template

def basic_metrics(returns: pd.Series | pd.DataFrame) -> dict[str, float]:
    r = returns if isinstance(returns, pd.Series) else returns.iloc[:,0]
    r = r.fillna(0.0)
    ann = 252
    sharpe = r.mean()*ann / (r.std(ddof=1)*np.sqrt(ann) + 1e-12)
    downside = r.where(r<0, 0.0)
    sortino = r.mean()*ann / (downside.std(ddof=1)*np.sqrt(ann) + 1e-12)
    eq = (1 + r).cumprod()
    peak = eq.cummax()
    maxdd = float(((eq/peak)-1.0).min())
    cagr = float(eq.iloc[-1]**(ann/len(r)) - 1.0)
    es5 = float(np.nanmean(np.sort(r.values)[:max(1,int(0.05*len(r)))]))
    return {"Sharpe": float(sharpe), "Sortino": float(sortino), "MaxDD": maxdd, "CAGR": cagr, "ES5": es5}

_HTML = Template("""
<!doctype html><html><head><meta charset="utf-8"><title>OmegaQuant Report</title>
<style>body{font-family:system-ui,Arial} table{border-collapse:collapse} th,td{padding:6px 10px;border:1px solid #ddd}</style>
</head><body>
<h1>OmegaQuant Tear‑Sheet</h1>
<h2>Summary Metrics</h2>
{{ summary.to_html(float_format=lambda x: f"{x:.6f}") }}
<p>Educational use only.</p>
</body></html>
""")

def tear_sheet(equity: pd.Series, returns: pd.Series, trades: pd.DataFrame, out_path: Path):
    mets = basic_metrics(returns)
    df = pd.DataFrame(mets, index=["value"]).T
    html = _HTML.render(summary=df)
    Path(out_path).write_text(html, encoding="utf-8")
