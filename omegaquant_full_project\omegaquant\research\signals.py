
from __future__ import annotations
import numpy as np
import pandas as pd

def _safe_pct_change(df: pd.DataFrame) -> pd.DataFrame:
    return df.pct_change().replace([np.inf, -np.inf], np.nan).fillna(0.0)

def tsmom(prices: pd.DataFrame, lookback: int = 126) -> pd.DataFrame:
    rets = _safe_pct_change(prices)
    mom = prices.pct_change(lookback).fillna(0.0)
    sig = np.sign(mom).replace(0, 0.0)
    sig = sig.rolling(5).mean().fillna(0.0)
    return sig

def xsec_momentum(prices: pd.DataFrame, lookback: int = 126) -> pd.DataFrame:
    mom = prices.pct_change(lookback).fillna(0.0)
    ranks = mom.rank(axis=1, pct=True) - 0.5
    return ranks

def mean_reversion_z(prices: pd.DataFrame, lookback: int = 20) -> pd.DataFrame:
    rets = _safe_pct_change(prices)
    m = rets.rolling(lookback).mean()
    s = rets.rolling(lookback).std().replace(0, np.nan)
    z = -(rets - m) / s
    return z.fillna(0.0).clip(-2, 2)

def combine_signals(weights: dict[str, float], **signals) -> pd.DataFrame:
    idx = next(iter(signals.values())).index
    cols = next(iter(signals.values())).columns
    out = pd.DataFrame(0.0, index=idx, columns=cols)
    for name, w in weights.items():
        if name in signals:
            out += float(w) * signals[name].reindex(index=idx, columns=cols).fillna(0.0)
    # Normalize to [-1,1]
    out = out.clip(-1, 1)
    return out

def target_shares_from_signal(signal: pd.DataFrame, prices: pd.DataFrame, target_vol: float = 0.10, max_gross: float = 2.0) -> pd.DataFrame:
    rets = prices.pct_change().fillna(0.0)
    vol = rets.rolling(63).std().replace(0, np.nan).fillna(method="bfill").fillna(1e-6)
    # inverse-vol scaled signals
    w = (signal / vol).replace([np.inf, -np.inf], 0.0)
    w = w.divide(w.abs().sum(axis=1).replace(0, np.nan), axis=0).fillna(0.0) * max_gross
    # Convert weights to shares assuming unit equity baseline
    shares = w.divide(prices.replace(0, np.nan), axis=0).fillna(0.0)
    return shares
