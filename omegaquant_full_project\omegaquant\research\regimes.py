
from __future__ import annotations
import numpy as np
import pandas as pd

def hmm_volatility_regimes(returns: pd.Series, n_states: int = 2, seed: int = 42):
    r = returns.fillna(0.0).values.reshape(-1,1)
    try:
        from hmmlearn.hmm import GaussianHMM
        hmm = GaussianHMM(n_components=n_states, covariance_type="diag", random_state=seed, n_iter=200)
        hmm.fit(np.abs(r)); states = hmm.predict(np.abs(r))
        return pd.Series(states, index=returns.index, name="regime")
    except Exception:
        vol = returns.rolling(63).std().bfill(); thr = vol.quantile(0.5); return (vol > thr).astype(int).rename("regime")

def regime_scaler(states: pd.Series, low_vol_scale: float = 1.0, high_vol_scale: float = 0.6) -> pd.Series:
    return states.map(lambda s: high_vol_scale if int(s) == 1 else low_vol_scale).astype(float)
