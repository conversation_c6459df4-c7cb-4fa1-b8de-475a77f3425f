
import pandas as pd
from hypothesis import given, strategies as st
from omegaquant.research.regimes import regime_scaler

@given(st.lists(st.integers(min_value=0, max_value=1), min_size=10, max_size=200))
def test_scaler_bounds(lst):
    idx = pd.date_range("2021-01-01", periods=len(lst), freq="B", tz="UTC")
    s = pd.Series(lst, index=idx)
    scale = regime_scaler(s, 1.0, 0.5)
    assert (scale >= 0.0).all() and (scale <= 1.1).all()
