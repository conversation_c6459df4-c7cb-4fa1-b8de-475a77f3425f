
[build-system]
requires = ["setuptools>=68", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "omegaquant"
version = "0.3.0"
description = "OmegaQuant: research-grade algo trading with advanced microstructure, rigorous validation, and fast paths."
readme = "README.md"
requires-python = ">=3.11"
license = {text = "MIT"}
authors = [{name = "OmegaQuant Contributors"}]
dependencies = [
  "numpy>=1.26",
  "pandas>=2.1",
  "scipy>=1.11",
  "numba>=0.58",
  "pyyaml>=6.0",
  "jinja2>=3.1",
  "matplotlib>=3.8",
  "typing-extensions>=4.9"
]

[project.optional-dependencies]
accel = ["polars>=0.20", "pyarrow>=15", "jax>=0.4.26", "jaxlib"]
max = [
  "polars>=0.20",
  "pyarrow>=15",
  "jax>=0.4.26",
  "jaxlib",
  "hmmlearn>=0.2.8",
  "exchange_calendars>=4.5.5",
  "numba>=0.58"
]
dev = [
  "pytest>=7.4",
  "hypothesis>=6.98",
  "coverage>=7.4",
  "mypy>=1.8",
  "ruff>=0.3",
  "mkdocs>=1.5",
  "mkdocs-material>=9.5"
]

[project.scripts]
omegaquant = "omegaquant.cli.main:main"
