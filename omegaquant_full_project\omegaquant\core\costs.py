
from __future__ import annotations
from dataclasses import dataclass
import numpy as np

@dataclass
class CostModel:
    commission_bps: float = 1.0

def square_root_impact_bps(q_dollar: float, dollar_vol: float, k: float = 10.0) -> float:
    """Square-root impact model (bps)."""
    if dollar_vol <= 0 or q_dollar <= 0:
        return 0.0
    qd = min(q_dollar, dollar_vol)
    return float(k * np.sqrt(qd / dollar_vol))

def almgren_chriss_schedule(total_qty: float, n_slices: int, risk_aversion: float = 0.0):
    """Simple AC schedule; exponential if risk_aversion>0 else equal slices."""
    if n_slices <= 1:
        return [total_qty]
    sgn = 1.0 if total_qty >= 0 else -1.0
    q = abs(total_qty)
    if risk_aversion <= 1e-12:
        each = q / n_slices
        return [sgn * each for _ in range(n_slices)]
    lam = max(1e-6, risk_aversion)
    weights = np.exp(-lam * np.arange(n_slices))
    weights = weights / weights.sum()
    return list(sgn * q * weights)
