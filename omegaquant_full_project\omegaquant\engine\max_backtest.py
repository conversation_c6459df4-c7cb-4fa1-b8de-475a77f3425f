
from __future__ import annotations
from dataclasses import dataclass
from pathlib import Path
import time
from typing import Dict, Any
import numpy as np
import pandas as pd
import yaml

from ..core.portfolio import Portfolio
from ..core.execution import generate_orders_from_target
from ..core.execution_l1 import simulate_bar_l1
from ..core.costs_adv import AdvCostModel
from ..research import signals as sig
from ..research.regimes import hmm_volatility_regimes, regime_scaler
from ..research.risk_models import ledoit_wolf_cov, hrp_weights, cvar_minimization
from ..reporting.metrics import tear_sheet, basic_metrics
from ..reporting.attribution import brinson_allocation_selection, write_report
from ..io.quotes import synthesize_l1_quotes_from_prices
from ..io.data import synthetic_gbm

@dataclass
class BacktestResultMax:
    equity: pd.Series
    returns: pd.Series
    trades: pd.DataFrame
    metrics: Dict[str, float]
    out_dir: Path

def _ensure_outdir() -> Path:
    ts = time.strftime("%Y%m%d-%H%M%S")
    out = Path("outputs") / f"run-{ts}-max"
    out.mkdir(parents=True, exist_ok=True)
    return out

def run_backtest_max(cfg: Dict[str, Any]) -> BacktestResultMax:
    seed = int(cfg.get("seed", 42))
    rng = np.random.RandomState(seed)
    tickers = cfg.get("tickers", ["AAPL","MSFT","AMZN","GOOG","META","NFLX","NVDA","TSLA"])
    n_days = int(cfg.get("n_days", 252*5))
    prices = synthetic_gbm(tickers, n_days, seed=seed)
    bid, ask, bid_size, ask_size = synthesize_l1_quotes_from_prices(
        prices, spread_bps=cfg.get("spread_bps", 6.0), depth=cfg.get("depth_dollar", 1_200_000.0), seed=seed+9
    )
    adv_dollar = ((bid + ask) / 2.0) * (bid_size + ask_size)

    s1 = sig.tsmom(prices, lookback=126)
    s2 = sig.xsec_momentum(prices, lookback=126)
    s3 = sig.mean_reversion_z(prices, lookback=20)
    base_signal = sig.combine_signals({"tsmom":0.4,"xsec":0.4,"meanrev":0.2}, tsmom=s1, xsec=s2, meanrev=s3)

    port_rets_proxy = prices.pct_change().mean(axis=1).fillna(0.0)
    regimes = hmm_volatility_regimes(port_rets_proxy, n_states=2, seed=seed)
    scale = regime_scaler(regimes, low_vol_scale=float(cfg.get("low_vol_scale",1.0)), high_vol_scale=float(cfg.get("high_vol_scale",0.6)))
    signal = base_signal.mul(scale, axis=0).clip(-1, 1)

    rets = prices.pct_change().fillna(0.0)
    cov = ledoit_wolf_cov(rets)
    hrp_w = hrp_weights(cov).reindex(prices.columns).fillna(0.0)
    signed = np.sign(signal.replace(0.0, np.nan)).fillna(0.0)
    w_template = (signed.abs().multiply(hrp_w, axis=1))
    w_template = w_template.divide(w_template.sum(axis=1).replace(0.0, np.nan), axis=0).fillna(0.0)

    if bool(cfg.get("use_cvar", True)):
        monthly = rets.resample("M").apply(lambda x: x)
        refined = []
        for dt, chunk in monthly.groupby(level=0):
            w = cvar_minimization(chunk.droplevel(0), alpha=float(cfg.get("cvar_alpha",0.05)), long_only=True, bounds=(0.0, float(cfg.get("pos_max",0.2))))
            refined.append((dt, w))
        refined_w = pd.DataFrame({dt: w for dt,w in refined}).T.reindex(prices.index, method="ffill").fillna(method="bfill")
        refined_w = refined_w.reindex(columns=prices.columns, fill_value=0.0)
        w_template = w_template.combine_first(refined_w)

    targets = sig.target_shares_from_signal(w_template, prices, target_vol=cfg.get("target_vol", 0.10), max_gross=cfg.get("max_gross", 2.0))

    port = Portfolio(leverage_max=cfg.get("max_gross", 2.0), net_max=cfg.get("max_net", 0.7), pos_max=cfg.get("pos_max", 0.2))
    port.initialize(prices.index, list(prices.columns))
    adv_cost = AdvCostModel(commission_bps=float(cfg.get("commission_bps",1.0)), maker_fee_bps=float(cfg.get("maker_fee_bps",-0.2)),
                            taker_fee_bps=float(cfg.get("taker_fee_bps",2.5)), exchange_fee_bps=float(cfg.get("exchange_fee_bps",0.0)))
    use_numba = bool(cfg.get("use_numba", False))

    all_trades = []
    for when in prices.index:
        port.accrue_borrow(when, prices.loc[when])
        orders = generate_orders_from_target(port.positions.loc[when], targets.loc[when])
        if "latency_ms" in cfg or "queue_ahead" in cfg or "venue_liquidity" in cfg:
            lat = int(cfg.get("latency_ms", 50)); qahead = float(cfg.get("queue_ahead", 0.0)); vliq = float(cfg.get("venue_liquidity", 1.0))
            for o in orders.values():
                o.latency_ms = lat; o.queue_ahead = qahead; o.venue_liquidity = vliq

        fills = simulate_bar_l1(
            when,
            bid.loc[when], ask.loc[when], bid_size.loc[when], ask_size.loc[when],
            adv_dollar.loc[when], orders, adv_cost,
            ac_slices=int(cfg.get("ac_slices", 1)), ac_risk_aversion=float(cfg.get("ac_risk",0.0)),
            rng=rng, use_numba=use_numba
        )

        if fills:
            port.apply_fills(when, fills)

        for a, o in orders.items():
            q, px = fills.get(a, (0.0, np.nan))
            all_trades.append({"time": when, "asset": a, "desired_qty": float(o.qty), "qty": float(q), "px": float(px) if not np.isnan(px) else np.nan})

        port.enforce_constraints(when, prices.loc[when])

    equity = port.mark_to_market(prices)
    rets_eq = equity.pct_change().fillna(0.0)
    trades = pd.DataFrame(all_trades).set_index("time") if all_trades else pd.DataFrame(columns=["time","asset","desired_qty","qty","px"]).set_index("time")
    if "desired_qty" in trades.columns:
        trades["fill_rate"] = (trades["qty"].abs() / trades["desired_qty"].abs().replace(0.0, np.nan) * 100).fillna(0.0)

    out_dir = _ensure_outdir()
    equity.to_csv(out_dir / "equity.csv"); rets_eq.to_csv(out_dir / "returns.csv")
    prices.to_csv(out_dir / "prices.csv"); 
    bid.to_csv(out_dir / "bid.csv"); ask.to_csv(out_dir / "ask.csv")
    bid_size.to_csv(out_dir / "bid_size.csv"); ask_size.to_csv(out_dir / "ask_size.csv")
    trades.to_csv(out_dir / "trades.csv")

    tear_sheet(equity, rets_eq, trades, out_path=out_dir / "report.html")
    mets = basic_metrics(rets_eq)
    with open(out_dir / "metrics.yaml","w") as f:
        yaml.safe_dump({k: float(v) for k,v in mets.items()}, f)

    asset_rets = prices.pct_change().fillna(0.0)
    w_bench = pd.Series(1.0/len(prices.columns), index=prices.columns)
    with np.errstate(divide="ignore", invalid="ignore"):
        port_weights = (port.positions * prices).divide((port.positions * prices).sum(axis=1), axis=0).fillna(0.0)
    attrib = brinson_allocation_selection(asset_rets, w_bench, port_weights)
    from ..reporting.tca import tca_from_files
    tca_from_files(out_dir / "trades.csv", out_dir / "prices.csv", out_dir / "tca.html")
    write_report(attrib, None, out_dir / "attribution.html")

    return BacktestResultMax(equity=equity, returns=rets_eq, trades=trades, metrics=mets, out_dir=out_dir)
