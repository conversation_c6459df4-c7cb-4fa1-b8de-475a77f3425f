
from __future__ import annotations
from dataclasses import dataclass
from enum import Enum, auto
from typing import Dict, Tu<PERSON>, Optional
import numpy as np
import pandas as pd

from .costs import CostModel

class OrderType(Enum):
    MARKET = auto()
    LIMIT = auto()
    STOP = auto()
    ICEBERG = auto()

@dataclass
class Order:
    qty: float
    order_type: OrderType = OrderType.MARKET
    limit_price: Optional[float] = None
    iceberg_peak: Optional[float] = None
    latency_ms: int = 50
    queue_ahead: float = 0.0
    venue_liquidity: float = 1.0

def compute_delta(current: float, target: float) -> float:
    return float(target - current)

def generate_orders_from_target(current_row: pd.Series, target_row: pd.Series) -> Dict[str, Order]:
    orders: Dict[str, Order] = {}
    for a in target_row.index:
        d = compute_delta(float(current_row.get(a, 0.0)), float(target_row.get(a, 0.0)))
        if abs(d) > 1e-9:
            orders[a] = Order(qty=d)
    return orders

class ExecutionEngine:
    """Baseline bar-mid execution with commission only (for non-L1 path)."""
    def __init__(self, cost_model: CostModel, rng: Optional[np.random.RandomState] = None):
        self.cost = cost_model
        self.rng = rng or np.random.RandomState(42)

    def simulate_bar(self, when: pd.Timestamp, price_row: pd.Series, volume_row: pd.Series,
                     orders: Dict[str, Order]) -> Dict[str, Tuple[float, float]]:
        fills: Dict[str, Tuple[float, float]] = {}
        for a, o in orders.items():
            px = float(price_row.get(a, np.nan))
            if not np.isfinite(px) or px <= 0 or o.qty == 0.0:
                continue
            side = 1.0 if o.qty > 0 else -1.0
            fee_bps = self.cost.commission_bps
            exec_px = px * (1.0 + side * (fee_bps * 1e-4))
            fills[a] = (o.qty, exec_px)
        return fills
