
from __future__ import annotations
import pandas as pd

def sessions(start: str | pd.Timestamp, end: str | pd.Timestamp, venue: str = "XNYS") -> pd.DatetimeIndex:
    """Exchange sessions via exchange_calendars if available; fallback to US federal business days."""
    try:
        import exchange_calendars as xc
        cal = xc.get_calendar(venue)
        sched = cal.schedule(start_date=pd.to_datetime(start).date(), end_date=pd.to_datetime(end).date())
        return pd.DatetimeIndex(sched.index.tz_localize("UTC"))
    except Exception:
        from pandas.tseries.holiday import USFederalHolidayCalendar
        from pandas.tseries.offsets import CustomBusinessDay
        bday = CustomBusinessDay(calendar=USFederalHolidayCalendar())
        return pd.date_range(start, end, freq=bday, tz="UTC")
